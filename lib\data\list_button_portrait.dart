import 'package:calculadora_xxi/icons/my_icons_icons.dart';
import 'package:flutter/material.dart';
import '../widget/button.dart';

const int divide = 4;

List<Widget> buttonPortrait_1_2 = [
  const ButtonUI(
    btnTxtNoVisible: '√(',
    btnSemantics: '<PERSON>z Cuadrada',
    btnDivide: divide,
    myIcons: MyIcons.raizcuadrada,
  ),
  const ButtonUI(
    btnTxtNoVisible: '%',
    btnSemantics: 'Porcentaje',
    btnDivide: divide,
    myIcons: MyIcons.porcentajes,
  ),
  const ButtonUI(
    btnTxtNoVisible: 'C',
    btnSemantics: 'Limpiar',
    btnDivide: divide,
    myIcons: MyIcons.borrartodo,
  ),
  const ButtonUI(
    btnTxtNoVisible: 'DEL',
    btnSemantics: 'Borrar',
    btnDivide: divide,
    myIcons: MyIcons.borrar,
  ),
  const ButtonUI(
    btnTxtNoVisible: '(',
    btnSemantics: 'Abrir Parentesis',
    btnDivide: divide,
    myIcons: MyIcons.parentesisabierto,
  ),
  const ButtonUI(
    btnTxtNoVisible: ')',
    btnSemantics: 'Cerrar Parentesis',
    btnDivide: divide,
    myIcons: MyIcons.parentesiscerrado,
  ),
  const ButtonUI(
    btnTxtNoVisible: 'π',
    btnSemantics: 'Pi',
    btnDivide: divide,
    myIcons: MyIcons.conspi,
  ),
  const ButtonUI(
    btnTxtNoVisible: '÷',
    btnSemantics: 'Entre',
    btnDivide: divide,
    myIcons: MyIcons.signoentre,
  ),
];

List<Widget> buttonPortraitFX_1_2 = [
  const ButtonUI(
    btnTxtNoVisible: 'sin(',
    btnSemantics: 'seno',
    btnDivide: divide,
    myIcons: MyIcons.seno,
  ),
  const ButtonUI(
    btnTxtNoVisible: 'cos(',
    btnSemantics: 'coseno',
    btnDivide: divide,
    myIcons: MyIcons.coseno,
  ),
  const ButtonUI(
    btnTxtNoVisible: 'tan(',
    btnSemantics: 'tangente',
    btnDivide: divide,
    myIcons: MyIcons.tangente,
  ),
  const ButtonUI(
    btnTxtNoVisible: '⅓',
    btnSemantics: 'raiz cubica',
    btnDivide: divide,
    myIcons: MyIcons.raizcubica,
  ),
  const ButtonUI(
    btnTxtNoVisible: 'sec(',
    btnSemantics: 'secante',
    btnDivide: divide,
    myIcons: MyIcons.secante,
  ),
  const ButtonUI(
    btnTxtNoVisible: 'csc(',
    btnSemantics: 'Cosecante',
    btnDivide: divide,
    myIcons: MyIcons.cosecante,
  ),
  const ButtonUI(
    btnTxtNoVisible: 'cot(',
    btnSemantics: 'Cotangente',
    btnDivide: divide,
    myIcons: MyIcons.cotangente,
  ),
  const ButtonUI(
    btnTxtNoVisible: '^',
    btnSemantics: 'x a la n',
    btnDivide: divide,
    myIcons: MyIcons.xn,
  ),
];

List buttonPortrait = [...buttonPortrait_1_2, ...buttonPortrait_3_6];
List buttonPortraitFX = [...buttonPortraitFX_1_2, ...buttonPortrait_3_6];

List<Widget> buttonPortrait_3_6 = [
  const ButtonUI(
    btnTxtNoVisible: '7',
    btnSemantics: 'siete',
    btnDivide: divide,
    myIcons: MyIcons.nsiete,
  ),
  const ButtonUI(
    btnTxtNoVisible: '8',
    btnSemantics: 'ocho',
    btnDivide: divide,
    myIcons: MyIcons.nocho,
  ),
  const ButtonUI(
    btnTxtNoVisible: '9',
    btnSemantics: 'nueve',
    btnDivide: divide,
    myIcons: MyIcons.nnueve,
  ),
  const ButtonUI(
    btnTxtNoVisible: 'x',
    btnSemantics: 'Por',
    btnDivide: divide,
    myIcons: MyIcons.signopor,
  ),
  const ButtonUI(
    btnTxtNoVisible: '4',
    btnSemantics: 'cuatro',
    btnDivide: divide,
    myIcons: MyIcons.ncuatro,
  ),
  const ButtonUI(
    btnTxtNoVisible: '5',
    btnSemantics: 'cinco',
    btnDivide: divide,
    myIcons: MyIcons.ncinco,
  ),
  const ButtonUI(
    btnTxtNoVisible: '6',
    btnSemantics: 'seis',
    btnDivide: divide,
    myIcons: MyIcons.nseis,
  ),
  const ButtonUI(
    btnTxtNoVisible: '-',
    btnSemantics: 'Menos',
    btnDivide: divide,
    myIcons: MyIcons.signomenos,
  ),
  const ButtonUI(
    btnTxtNoVisible: '1',
    btnSemantics: 'uno',
    btnDivide: divide,
    myIcons: MyIcons.nuno,
  ),
  const ButtonUI(
    btnTxtNoVisible: '2',
    btnSemantics: 'dos',
    btnDivide: divide,
    myIcons: MyIcons.ndos,
  ),
  const ButtonUI(
    btnTxtNoVisible: '3',
    btnSemantics: 'tres',
    btnDivide: divide,
    myIcons: MyIcons.ntres,
  ),
  const ButtonUI(
    btnTxtNoVisible: '+',
    btnSemantics: 'Mas',
    btnDivide: divide,
    myIcons: MyIcons.signomas,
  ),
  const ButtonUI(
    btnTxtNoVisible: 'FxVertical',
    btnSemantics: 'Funciones',
    btnDivide: divide,
    myIcons: MyIcons.funciones,
  ),
  const ButtonUI(
    btnTxtNoVisible: '0',
    btnSemantics: 'cero',
    btnDivide: divide,
    myIcons: MyIcons.ncero,
  ),
  const ButtonUI(
    btnTxtNoVisible: ',',
    btnSemantics: 'coma',
    btnDivide: divide,
    myIcons: MyIcons.coma,
  ),
  const ButtonUI(
    btnTxtNoVisible: '=',
    btnSemantics: 'igual',
    btnDivide: divide,
    myIcons: MyIcons.signoigual,
  ),
];
