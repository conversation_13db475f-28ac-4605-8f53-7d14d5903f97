import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:table_calendar/table_calendar.dart';
import 'package:intl/intl.dart';
import 'package:calculadora_xxi/provider/history_provider.dart';
import 'package:calculadora_xxi/provider/calculator_provider.dart';
import 'package:calculadora_xxi/models/operation_history.dart';
import 'package:calculadora_xxi/ui/t9_input_dialog.dart';

class HistoryScreen extends StatefulWidget {
  const HistoryScreen({super.key});

  @override
  State<HistoryScreen> createState() => _HistoryScreenState();
}

class _HistoryScreenState extends State<HistoryScreen> {
  DateTime _focusedDay = DateTime.now();
  CalendarFormat _calendarFormat =
      CalendarFormat.twoWeeks; // 2 semanas por defecto también en vertical
  CalendarFormat _calendarFormatHorizontal =
      CalendarFormat.twoWeeks; // 2 semanas por defecto en horizontal

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final historyProvider = Provider.of<HistoryProvider>(
        context,
        listen: false,
      );
      historyProvider.loadDatesWithOperations();
      historyProvider.loadOperationsByDate(DateTime.now());
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Consumer<HistoryProvider>(
        builder: (context, historyProvider, child) {
          return OrientationBuilder(
            builder: (context, orientation) {
              if (orientation == Orientation.landscape) {
                // Layout horizontal con AppBar y 2 columnas
                return Scaffold(
                  appBar: AppBar(title: const Text('Historial de Operaciones')),
                  body: Row(
                    children: [
                      // Columna 1: Calendario
                      Expanded(
                        flex: 2, // Calendario más compacto
                        child: Container(
                          margin: const EdgeInsets.all(4.0), // Reducir margen
                          decoration: BoxDecoration(
                            border: Border.all(color: Colors.grey),
                            borderRadius: BorderRadius.circular(8.0),
                          ),
                          child: TableCalendar<OperationHistory>(
                            firstDay: DateTime.utc(2020, 1, 1),
                            lastDay: DateTime.utc(2030, 12, 31),
                            focusedDay: _focusedDay,
                            calendarFormat: _calendarFormatHorizontal,
                            availableCalendarFormats: const {
                              CalendarFormat.month: 'Mes',
                              CalendarFormat.twoWeeks: '2 weeks',
                              CalendarFormat.week: 'Week',
                            },
                            selectedDayPredicate: (day) {
                              return isSameDay(
                                historyProvider.selectedDate,
                                day,
                              );
                            },
                            eventLoader: (day) {
                              // Solo marcar días con operaciones que tienen nombre personalizado
                              return historyProvider.datesWithNamedOperations
                                  .where((date) => isSameDay(date, day))
                                  .map(
                                    (date) => OperationHistory(
                                      id: '',
                                      expression: '',
                                      result: '',
                                      date: date,
                                    ),
                                  )
                                  .toList();
                            },
                            onDaySelected: (selectedDay, focusedDay) {
                              setState(() {
                                _focusedDay = focusedDay;
                              });
                              historyProvider.setSelectedDate(selectedDay);
                            },
                            onFormatChanged: (format) {
                              setState(() {
                                _calendarFormatHorizontal = format;
                              });
                            },
                            calendarStyle: const CalendarStyle(
                              outsideDaysVisible:
                                  true, // Mostrar días de otros meses
                              markerDecoration: BoxDecoration(
                                color: Colors.blue,
                                shape: BoxShape.circle,
                              ),
                              cellMargin: EdgeInsets.all(
                                1,
                              ), // Reducir margen entre celdas
                              defaultTextStyle: TextStyle(fontSize: 11),
                              weekendTextStyle: TextStyle(fontSize: 11),
                              selectedTextStyle: TextStyle(
                                fontSize: 11,
                                color: Colors.white,
                              ),
                              outsideTextStyle: TextStyle(
                                fontSize: 10,
                                color: Colors.grey,
                              ),
                              cellPadding: EdgeInsets.all(2), // Reducir padding
                            ),
                            headerStyle: HeaderStyle(
                              formatButtonVisible:
                                  true, // Mostrar botón de formato
                              titleCentered: true,
                              titleTextStyle: const TextStyle(fontSize: 13),
                              formatButtonTextStyle: const TextStyle(
                                fontSize: 11,
                              ),
                              formatButtonDecoration: BoxDecoration(
                                border: Border.all(color: Colors.grey),
                                borderRadius: const BorderRadius.all(
                                  Radius.circular(12),
                                ),
                              ),
                              leftChevronPadding: const EdgeInsets.all(4),
                              rightChevronPadding: const EdgeInsets.all(4),
                            ),
                            daysOfWeekStyle: const DaysOfWeekStyle(
                              weekdayStyle: TextStyle(fontSize: 9),
                              weekendStyle: TextStyle(fontSize: 9),
                            ),
                          ),
                        ),
                      ),

                      // Divisor vertical
                      const VerticalDivider(width: 1),

                      // Columna 2: Operaciones
                      Expanded(
                        flex: 3, // Más espacio para las operaciones
                        child: Column(
                          children: [
                            // Fecha seleccionada
                            Padding(
                              padding: const EdgeInsets.all(8.0),
                              child: Text(
                                'Operaciones del ${DateFormat('dd/MM/yyyy').format(historyProvider.selectedDate)}',
                                style: const TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),

                            // Lista de operaciones
                            Expanded(
                              child: historyProvider.operations.isEmpty
                                  ? const Center(
                                      child: Text(
                                        'No hay operaciones para esta fecha',
                                        style: TextStyle(
                                          fontSize: 16,
                                          color: Colors.grey,
                                        ),
                                      ),
                                    )
                                  : ListView.builder(
                                      itemCount:
                                          historyProvider.operations.length,
                                      itemBuilder: (context, index) {
                                        final operation =
                                            historyProvider.operations[index];
                                        return _buildOperationCard(
                                          context,
                                          operation,
                                          historyProvider,
                                        );
                                      },
                                    ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                );
              } else {
                // Layout vertical con AppBar
                return Scaffold(
                  appBar: AppBar(title: const Text('Historial de Operaciones')),
                  body: Column(
                    children: [
                      // Calendario
                      Container(
                        margin: const EdgeInsets.all(8.0),
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.grey),
                          borderRadius: BorderRadius.circular(8.0),
                        ),
                        child: TableCalendar<OperationHistory>(
                          firstDay: DateTime.utc(2020, 1, 1),
                          lastDay: DateTime.utc(2030, 12, 31),
                          focusedDay: _focusedDay,
                          calendarFormat: _calendarFormat,
                          availableCalendarFormats: const {
                            CalendarFormat.month: 'Mes',
                            CalendarFormat.twoWeeks: '2 weeks',
                            CalendarFormat.week: 'Week',
                          },
                          selectedDayPredicate: (day) {
                            return isSameDay(historyProvider.selectedDate, day);
                          },
                          eventLoader: (day) {
                            // Solo marcar días con operaciones que tienen nombre personalizado
                            return historyProvider.datesWithNamedOperations
                                .where((date) => isSameDay(date, day))
                                .map(
                                  (date) => OperationHistory(
                                    id: '',
                                    expression: '',
                                    result: '',
                                    date: date,
                                  ),
                                )
                                .toList();
                          },
                          onDaySelected: (selectedDay, focusedDay) {
                            setState(() {
                              _focusedDay = focusedDay;
                            });
                            historyProvider.setSelectedDate(selectedDay);
                          },
                          onFormatChanged: (format) {
                            setState(() {
                              _calendarFormat = format;
                            });
                          },
                          calendarStyle: const CalendarStyle(
                            outsideDaysVisible: false,
                            markerDecoration: BoxDecoration(
                              color: Colors.blue,
                              shape: BoxShape.circle,
                            ),
                          ),
                        ),
                      ),

                      // Fecha seleccionada
                      Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: Text(
                          'Operaciones del ${DateFormat('dd/MM/yyyy').format(historyProvider.selectedDate)}',
                          style: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),

                      // Lista de operaciones
                      Expanded(
                        child: historyProvider.operations.isEmpty
                            ? const Center(
                                child: Text(
                                  'No hay operaciones para esta fecha',
                                  style: TextStyle(
                                    fontSize: 16,
                                    color: Colors.grey,
                                  ),
                                ),
                              )
                            : ListView.builder(
                                itemCount: historyProvider.operations.length,
                                itemBuilder: (context, index) {
                                  final operation =
                                      historyProvider.operations[index];
                                  return _buildOperationCard(
                                    context,
                                    operation,
                                    historyProvider,
                                  );
                                },
                              ),
                      ),
                    ],
                  ),
                );
              }
            },
          );
        },
      ),
    );
  }

  Widget _buildOperationCard(
    BuildContext context,
    OperationHistory operation,
    HistoryProvider historyProvider,
  ) {
    // Determinar el color y estilo según el tipo de operación
    final bool isManualOperation = !operation.isAutoSaved;
    final bool hasCustomName =
        operation.customName != null && operation.customName!.isNotEmpty;

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
      // Color diferente para operaciones manuales vs automáticas
      color: isManualOperation
          ? (hasCustomName ? Colors.blue.shade50 : Colors.green.shade50)
          : Colors.grey.shade50,
      child: ListTile(
        onTap: () => _loadOperationInCalculator(context, operation),
        // Icono indicador del tipo de operación
        leading: Icon(
          hasCustomName
              ? Icons.bookmark
              : (isManualOperation ? Icons.save : Icons.history),
          color: hasCustomName
              ? Colors.blue
              : (isManualOperation ? Colors.green : Colors.grey),
          size: 20,
        ),
        title: Text(
          _getDisplayTitle(operation),
          style: TextStyle(
            fontWeight: hasCustomName ? FontWeight.bold : FontWeight.w500,
            color: hasCustomName ? Colors.blue.shade800 : Colors.black87,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Expresión: ${operation.expression}'),
            Text('Resultado: ${operation.result}'),
            Text('Hora: ${DateFormat('HH:mm:ss').format(operation.date)}'),
            // Indicador del tipo de operación
            Text(
              hasCustomName
                  ? '📌 Operación con nombre'
                  : (isManualOperation
                        ? '💾 Guardado manual'
                        : '⚡ Guardado automático'),
              style: TextStyle(
                color: hasCustomName
                    ? Colors.blue
                    : (isManualOperation ? Colors.green : Colors.grey),
                fontSize: 11,
                fontStyle: FontStyle.italic,
              ),
            ),
            const Text(
              'Toca para cargar en calculadora',
              style: TextStyle(color: Colors.blue, fontSize: 12),
            ),
          ],
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            IconButton(
              icon: const Icon(Icons.edit),
              onPressed: () =>
                  _showNameDialog(context, operation, historyProvider),
            ),
            IconButton(
              icon: const Icon(Icons.delete),
              onPressed: () =>
                  _showDeleteDialog(context, operation, historyProvider),
            ),
          ],
        ),
      ),
    );
  }

  // Método para obtener el título de visualización limitado a 10 caracteres
  String _getDisplayTitle(OperationHistory operation) {
    String title = operation.customName ?? operation.expression;
    if (title.length > 10) {
      return '${title.substring(0, 10)}...';
    }
    return title;
  }

  void _loadOperationInCalculator(
    BuildContext context,
    OperationHistory operation,
  ) {
    final calculatorProvider = Provider.of<CalculatorProvider>(
      context,
      listen: false,
    );

    // Cargar la operación para editar
    calculatorProvider.loadOperationForEditing(
      operation.id,
      operation.expression,
    );

    Navigator.of(context).pop();
  }

  void _showNameDialog(
    BuildContext context,
    OperationHistory operation,
    HistoryProvider historyProvider,
  ) {
    showDialog(
      context: context,
      builder: (context) => T9InputDialog(
        title: 'Nombrar Operación',
        initialText: operation.customName ?? '',
        onSave: (name) {
          historyProvider.updateOperationName(operation.id, name);
        },
      ),
    );
  }

  void _showDeleteDialog(
    BuildContext context,
    OperationHistory operation,
    HistoryProvider historyProvider,
  ) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Eliminar Operación'),
        content: Text(
          '¿Estás seguro de que quieres eliminar "${operation.customName ?? operation.expression}"?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancelar'),
          ),
          TextButton(
            onPressed: () {
              historyProvider.deleteOperation(operation.id);
              Navigator.of(context).pop();
            },
            child: const Text('Eliminar'),
          ),
        ],
      ),
    );
  }
}
