import 'dart:async';
import 'package:flutter/material.dart';

class T9InputDialog extends StatefulWidget {
  final String title;
  final String initialText;
  final Function(String) onSave;

  const T9InputDialog({
    super.key,
    required this.title,
    required this.initialText,
    required this.onSave,
  });

  @override
  State<T9InputDialog> createState() => _T9InputDialogState();
}

class _T9InputDialogState extends State<T9InputDialog> {
  String _currentText = '';
  String _currentInput = '';
  int _currentKey = -1;
  int _pressCount = 0;
  Timer? _autoConfirmTimer;

  // Mapeo T9: cada número tiene sus letras correspondientes
  final Map<int, List<String>> _t9Map = {
    1: ['1'],
    2: ['a', 'b', 'c', '2'],
    3: ['d', 'e', 'f', '3'],
    4: ['g', 'h', 'i', '4'],
    5: ['j', 'k', 'l', '5'],
    6: ['m', 'n', 'o', '6'],
    7: ['p', 'q', 'r', 's', '7'],
    8: ['t', 'u', 'v', '8'],
    9: ['w', 'x', 'y', 'z', '9'],
    0: [' ', '0'],
  };

  @override
  void initState() {
    super.initState();
    _currentText = widget.initialText;
  }

  void _onKeyPressed(int key) {
    // Cancelar timer anterior si existe
    _autoConfirmTimer?.cancel();

    setState(() {
      if (key == _currentKey) {
        // Misma tecla presionada, ciclar por las letras
        _pressCount++;
        if (_pressCount >= _t9Map[key]!.length) {
          _pressCount = 0;
        }
      } else {
        // Nueva tecla presionada, confirmar la anterior y empezar nueva
        _confirmCurrentInput();
        _currentKey = key;
        _pressCount = 0;
      }

      if (_t9Map.containsKey(key)) {
        _currentInput = _t9Map[key]![_pressCount];
      }
    });

    // Iniciar nuevo timer para auto-confirmar después de 1 segundo
    _autoConfirmTimer = Timer(const Duration(seconds: 1), () {
      if (mounted) {
        setState(() {
          _confirmCurrentInput();
        });
      }
    });
  }

  void _confirmCurrentInput() {
    if (_currentInput.isNotEmpty) {
      _currentText += _currentInput;
      _currentInput = '';
      _currentKey = -1;
      _pressCount = 0;
    }
  }

  void _onSpace() {
    _autoConfirmTimer?.cancel();
    setState(() {
      _confirmCurrentInput();
      _currentText += ' ';
    });
  }

  void _onBackspace() {
    _autoConfirmTimer?.cancel();
    setState(() {
      if (_currentInput.isNotEmpty) {
        // Si hay entrada actual, limpiarla
        _currentInput = '';
        _currentKey = -1;
        _pressCount = 0;
      } else if (_currentText.isNotEmpty) {
        // Si no hay entrada actual, borrar último carácter del texto
        _currentText = _currentText.substring(0, _currentText.length - 1);
      }
    });
  }

  void _onSave() {
    _confirmCurrentInput();
    widget.onSave(_currentText);
    Navigator.of(context).pop();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(widget.title),
      content: SizedBox(
        width: double.maxFinite,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Campo de texto que muestra el resultado
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                _currentText + _currentInput,
                style: const TextStyle(fontSize: 16),
                maxLines: 3,
              ),
            ),
            const SizedBox(height: 16),

            // Instrucciones
            const Text(
              'Presiona las teclas múltiples veces para seleccionar la letra',
              style: TextStyle(fontSize: 12, color: Colors.grey),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),

            // Teclado T9
            _buildT9Keyboard(),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancelar'),
        ),
        TextButton(onPressed: _onSave, child: const Text('Guardar')),
      ],
    );
  }

  Widget _buildT9Keyboard() {
    return Column(
      children: [
        // Fila 1: 1, 2, 3
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            _buildT9Key(1, '1'),
            _buildT9Key(2, 'ABC'),
            _buildT9Key(3, 'DEF'),
          ],
        ),
        const SizedBox(height: 8),

        // Fila 2: 4, 5, 6
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            _buildT9Key(4, 'GHI'),
            _buildT9Key(5, 'JKL'),
            _buildT9Key(6, 'MNO'),
          ],
        ),
        const SizedBox(height: 8),

        // Fila 3: 7, 8, 9
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            _buildT9Key(7, 'PQRS'),
            _buildT9Key(8, 'TUV'),
            _buildT9Key(9, 'WXYZ'),
          ],
        ),
        const SizedBox(height: 8),

        // Fila 4: Espacio, 0, Borrar
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            _buildActionKey('ESP', _onSpace),
            _buildT9Key(0, '0'),
            _buildActionKey('⌫', _onBackspace),
          ],
        ),
      ],
    );
  }

  Widget _buildT9Key(int number, String letters) {
    bool isActive = _currentKey == number;

    return GestureDetector(
      onTap: () => _onKeyPressed(number),
      child: Container(
        width: 60,
        height: 60,
        decoration: BoxDecoration(
          color: isActive ? Colors.blue[100] : Colors.grey[200],
          border: Border.all(color: isActive ? Colors.blue : Colors.grey),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              number.toString(),
              style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            Text(
              letters,
              style: const TextStyle(fontSize: 10, color: Colors.grey),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionKey(String label, VoidCallback onTap) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 60,
        height: 60,
        decoration: BoxDecoration(
          color: Colors.grey[300],
          border: Border.all(color: Colors.grey),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Center(
          child: Text(
            label,
            style: const TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
          ),
        ),
      ),
    );
  }

  @override
  void dispose() {
    _autoConfirmTimer?.cancel();
    super.dispose();
  }
}
