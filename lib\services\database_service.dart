import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import '../models/operation_history.dart';

class DatabaseService {
  static Database? _database;
  static const String tableName = 'operation_history';

  static Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    return _database!;
  }

  static Future<Database> _initDatabase() async {
    String path = join(await getDatabasesPath(), 'calculator_history.db');
    return await openDatabase(
      path,
      version: 2, // Incrementar versión para migración
      onCreate: _createDatabase,
      onUpgrade: _upgradeDatabase,
    );
  }

  static Future<void> _createDatabase(Database db, int version) async {
    await db.execute('''
      CREATE TABLE $tableName (
        id TEXT PRIMARY KEY,
        expression TEXT NOT NULL,
        result TEXT NOT NULL,
        date INTEGER NOT NULL,
        customName TEXT,
        isAutoSaved INTEGER DEFAULT 0
      )
    ''');
  }

  static Future<void> _upgradeDatabase(
    Database db,
    int oldVersion,
    int newVersion,
  ) async {
    if (oldVersion < 2) {
      // Agregar el campo isAutoSaved a la tabla existente
      await db.execute(
        'ALTER TABLE $tableName ADD COLUMN isAutoSaved INTEGER DEFAULT 0',
      );
    }
  }

  static Future<void> insertOperation(OperationHistory operation) async {
    final db = await database;
    await db.insert(
      tableName,
      operation.toMap(),
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  // Método específico para guardar operaciones automáticamente (cuando se presiona =)
  static Future<void> insertAutoOperation(
    String expression,
    String result,
  ) async {
    final operation = OperationHistory(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      expression: expression,
      result: result,
      date: DateTime.now(),
      isAutoSaved: true, // Marcado como guardado automático
    );

    await insertOperation(operation);
  }

  static Future<List<OperationHistory>> getOperationsByDate(
    DateTime date,
  ) async {
    final db = await database;
    final startOfDay = DateTime(date.year, date.month, date.day);
    final endOfDay = startOfDay.add(const Duration(days: 1));

    final List<Map<String, dynamic>> maps = await db.query(
      tableName,
      where: 'date >= ? AND date < ?',
      whereArgs: [
        startOfDay.millisecondsSinceEpoch,
        endOfDay.millisecondsSinceEpoch,
      ],
      // Ordenar: primero las operaciones con nombre personalizado, luego las manuales, luego las automáticas
      orderBy:
          'CASE WHEN customName IS NOT NULL AND customName != "" THEN 0 ELSE 1 END, isAutoSaved ASC, date DESC',
    );

    return List.generate(maps.length, (i) {
      return OperationHistory.fromMap(maps[i]);
    });
  }

  static Future<List<OperationHistory>> getAllOperations() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      tableName,
      orderBy: 'date DESC',
    );

    return List.generate(maps.length, (i) {
      return OperationHistory.fromMap(maps[i]);
    });
  }

  static Future<void> updateOperationName(String id, String customName) async {
    final db = await database;
    await db.update(
      tableName,
      {'customName': customName},
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  static Future<void> updateOperation(
    String id,
    String expression,
    String result,
    String? customName,
  ) async {
    final db = await database;
    await db.update(
      tableName,
      {'expression': expression, 'result': result, 'customName': customName},
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  static Future<void> deleteOperation(String id) async {
    final db = await database;
    await db.delete(tableName, where: 'id = ?', whereArgs: [id]);
  }

  // Obtener fechas que tienen operaciones (todas)
  static Future<List<DateTime>> getDatesWithOperations() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.rawQuery('''
      SELECT DISTINCT date(date/1000, 'unixepoch') as date_only
      FROM $tableName
      ORDER BY date_only DESC
    ''');

    return maps.map((map) {
      return DateTime.parse(map['date_only']);
    }).toList();
  }

  // Obtener fechas que tienen operaciones CON NOMBRE (para marcar en calendario)
  static Future<List<DateTime>> getDatesWithNamedOperations() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.rawQuery('''
      SELECT DISTINCT date(date/1000, 'unixepoch') as date_only
      FROM $tableName
      WHERE customName IS NOT NULL AND customName != ''
      ORDER BY date_only DESC
    ''');

    return maps.map((map) {
      return DateTime.parse(map['date_only']);
    }).toList();
  }
}
