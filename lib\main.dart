import 'package:calculadora_xxi/provider/calculator_provider.dart';
import 'package:calculadora_xxi/provider/history_provider.dart';
import 'package:calculadora_xxi/ui/splash_screen.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

void main() {
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider<HistoryProvider>(
          create: (_) => HistoryProvider(),
        ),
        ChangeNotifierProxyProvider<HistoryProvider, CalculatorProvider>(
          create: (_) => CalculatorProvider(),
          update: (_, historyProvider, calculatorProvider) {
            calculatorProvider ??= CalculatorProvider();
            calculatorProvider.historyProvider = historyProvider;
            return calculatorProvider;
          },
        ),
      ],
      builder: (context, _) {
        return const MaterialApp(
          debugShowCheckedModeBanner: false,
          title: 'Calculadora XXI',
          home: SplashScreen(),
        );
      },
    );
  }
}
