import 'package:flutter/material.dart';
import '../provider/calculator_provider.dart';

class OperacionesLinea1Linea2 extends StatelessWidget {
  const OperacionesLinea1Linea2({super.key, required this.watch});

  final CalculatorProvider watch;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: Align(
        alignment: Alignment.bottomRight,
        child: ListView(
          reverse: true,
          children: [
            Operaciones(
              watch: watch,
              label: 'El Resultado',
              controller: watch.resultadosProvider,
            ),
            Operaciones(
              watch: watch,
              label: 'Las Operaciones',
              controller: watch.operacionesProvider,
            ),
          ],
        ),
      ),
    );
  }
}

class Operaciones extends StatelessWidget {
  const Operaciones({
    super.key,
    required this.watch,
    required this.label,
    required this.controller,
  });

  final CalculatorProvider watch;
  final String label;
  final TextEditingController controller;

  @override
  Widget build(BuildContext context) {
    // Solo permitir edición en el campo de operaciones, no en resultados
    bool isEditable = label == 'Las Operaciones';

    return Semantics(
      readOnly: !isEditable,
      textField: true,
      label: label,
      child: ExcludeSemantics(
        child: TextField(
          readOnly: true, // Siempre solo lectura para evitar teclado
          enableInteractiveSelection:
              isEditable, // Permitir selección solo en operaciones
          showCursor: isEditable, // Mostrar cursor solo en operaciones
          maxLines: null,
          decoration: const InputDecoration(border: InputBorder.none),
          controller: controller,
          textAlign: TextAlign.right,
          style: TextStyle(fontSize: (label == 'Las Operaciones' ? 22 : 30)),
          // Deshabilitar completamente el teclado
          keyboardType: TextInputType.none,
          onTap: isEditable
              ? () {
                  // Permitir posicionar cursor sin mostrar teclado
                  // El cursor se posicionará automáticamente donde se toque
                }
              : null,
        ),
      ),
    );
  }
}
