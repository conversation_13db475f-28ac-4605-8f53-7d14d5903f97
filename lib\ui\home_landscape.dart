import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../provider/calculator_provider.dart';
import 'operaciones_linea1_linea2.dart';

class LandscapeHome extends StatelessWidget {
  const LandscapeHome({super.key});

  @override
  Widget build(BuildContext context) {
    CalculatorProvider watch = context.watch<CalculatorProvider>();
    return Semantics(
      label: 'Pantalla y Teclado',
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            child: SizedBox(
              child: Container(
                margin: const EdgeInsets.symmetric(horizontal: 4),
                height: double.maxFinite,
                decoration: BoxDecoration(
                    border: Border.all(),
                    borderRadius: BorderRadius.circular(20.0)),
                child: OperacionesLinea1Linea2(watch: watch),
              ),
            ),
          ),
          Padding(
            padding: const EdgeInsets.only(left: 8.0, right: 8.0),
            child: Wrap(
              runSpacing: 8.0,
              spacing: 8.0,
              direction: Axis.vertical,
              children: List.generate(
                  watch.newListButtonProviderHorizontal.length,
                  (index) => watch.newListButtonProviderHorizontal[index]),
            ),
          ),
        ],
      ),
    );
  }
}
