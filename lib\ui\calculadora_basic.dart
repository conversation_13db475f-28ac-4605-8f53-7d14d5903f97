import 'package:calculadora_xxi/ui/home_landscape.dart';
import 'package:calculadora_xxi/ui/home_portrait.dart';
import 'package:calculadora_xxi/ui/history_screen.dart';
import 'package:calculadora_xxi/ui/t9_input_dialog.dart';
import 'package:calculadora_xxi/provider/calculator_provider.dart';
import 'package:calculadora_xxi/provider/history_provider.dart';
import 'package:calculadora_xxi/models/operation_history.dart';
import 'package:calculadora_xxi/services/database_service.dart';
import 'package:provider/provider.dart';
import 'package:flutter/material.dart';

class CalculadoraBasica extends StatefulWidget {
  const CalculadoraBasica({super.key});

  @override
  State<CalculadoraBasica> createState() => _CalculadoraBasicaState();
}

class _CalculadoraBasicaState extends State<CalculadoraBasica> {
  @override
  Widget build(BuildContext context) {
    const String title = 'Calculadora XXI';
    return Scaffold(
      backgroundColor: Colors.grey[200], // Fondo gris estándar
      appBar: AppBar(
        elevation: 2,
        centerTitle: true,
        title: Semantics(
          label: title,
          child: const ExcludeSemantics(
            child: Text(
              title,
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 26,
                color: Colors.black87,
              ),
            ),
          ),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.save, color: Colors.black87),
            onPressed: () => _showSaveDialog(context),
            tooltip: 'Guardar Operación',
          ),
          IconButton(
            icon: const Icon(Icons.history, color: Colors.black87),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(builder: (context) => const HistoryScreen()),
              );
            },
            tooltip: 'Historial',
          ),
        ],
      ),

      body: Padding(
        padding: const EdgeInsets.all(8.0),
        child: SafeArea(
          child: OrientationBuilder(
            builder: (_, Orientation orientation) {
              return (orientation == Orientation.portrait)
                  ? const PortraitHome()
                  : const LandscapeHome();
            },
          ),
        ),
      ),
    );
  }

  void _showSaveDialog(BuildContext context) {
    final calculatorProvider = context.read<CalculatorProvider>();
    final historyProvider = context.read<HistoryProvider>();

    // Verificar que hay una operación para guardar
    if (calculatorProvider.operacionesProvider.text.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('No hay operación para guardar'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    // Calcular el resultado si no existe
    String result = calculatorProvider.resultadosProvider.text;
    if (result.isEmpty) {
      calculatorProvider.calcular();
      result = calculatorProvider.resultadosProvider.text;
    }

    // Verificar si estamos editando una operación existente
    if (calculatorProvider.isEditingOperation) {
      _showEditOrCreateDialog(
        context,
        calculatorProvider,
        historyProvider,
        result,
      );
    } else {
      _showNameDialog(
        context,
        calculatorProvider,
        historyProvider,
        result,
        isUpdate: false,
      );
    }
  }

  void _showEditOrCreateDialog(
    BuildContext context,
    CalculatorProvider calculatorProvider,
    HistoryProvider historyProvider,
    String result,
  ) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Operación Existente'),
        content: const Text(
          'Esta operación ya existe en el historial. ¿Qué deseas hacer?',
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _showNameDialog(
                context,
                calculatorProvider,
                historyProvider,
                result,
                isUpdate: true,
              );
            },
            child: const Text('Actualizar'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              calculatorProvider.clearEditingState();
              _showNameDialog(
                context,
                calculatorProvider,
                historyProvider,
                result,
                isUpdate: false,
              );
            },
            child: const Text('Crear Nueva'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancelar'),
          ),
        ],
      ),
    );
  }

  void _showNameDialog(
    BuildContext context,
    CalculatorProvider calculatorProvider,
    HistoryProvider historyProvider,
    String result, {
    required bool isUpdate,
  }) {
    // Obtener el nombre actual si estamos actualizando
    String initialName = '';
    if (isUpdate && calculatorProvider.editingOperationId != null) {
      final operation = historyProvider.operations.firstWhere(
        (op) => op.id == calculatorProvider.editingOperationId,
        orElse: () => OperationHistory(
          id: '',
          expression: '',
          result: '',
          date: DateTime.now(),
        ),
      );
      initialName = operation.customName ?? '';
    }

    showDialog(
      context: context,
      builder: (context) => T9InputDialog(
        title: isUpdate ? 'Actualizar Operación' : 'Nombrar Operación',
        initialText: initialName,
        onSave: (name) {
          if (isUpdate && calculatorProvider.editingOperationId != null) {
            // Actualizar operación existente
            historyProvider.updateOperation(
              calculatorProvider.editingOperationId!,
              calculatorProvider.operacionesProvider.text,
              result,
              name.isNotEmpty ? name : null,
            );

            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(
                  'Operación actualizada: ${name.isNotEmpty ? name : calculatorProvider.operacionesProvider.text}',
                ),
                backgroundColor: Colors.blue,
              ),
            );
          } else {
            // Crear nueva operación manual (con nombre)
            if (name.isNotEmpty) {
              // Usar el método para convertir a operación manual
              historyProvider.convertToManualOperation(
                calculatorProvider.operacionesProvider.text,
                result,
                name,
              );
            } else {
              // Crear operación sin nombre (manual pero sin customName)
              final operation = OperationHistory(
                id: DateTime.now().millisecondsSinceEpoch.toString(),
                expression: calculatorProvider.operacionesProvider.text,
                result: result,
                date: DateTime.now(),
                isAutoSaved: false, // Manual
              );

              DatabaseService.insertOperation(operation);
            }

            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(
                  'Operación guardada: ${name.isNotEmpty ? name : calculatorProvider.operacionesProvider.text}',
                ),
                backgroundColor: Colors.green,
              ),
            );
          }

          // Limpiar estado de edición
          calculatorProvider.clearEditingState();
        },
      ),
    );
  }
}
