import 'package:calculadora_xxi/icons/my_icons_icons.dart';
import 'package:flutter/material.dart';
import '../widget/button.dart';

const int divide = 5;

List<Widget> buttonLandscape_1_2 = [
  const ButtonUI(
    btnTxtNoVisible: 'C',
    btnSemantics: 'Limpiar',
    btnDivide: divide,
    myIcons: MyIcons.borrartodo,
  ),
  const ButtonUI(
    btnTxtNoVisible: '√(',
    btnSemantics: 'Raiz Cuadrada',
    btnDivide: divide,
    myIcons: MyIcons.raizcuadrada,
  ),
  const ButtonUI(
    btnTxtNoVisible: '(',
    btnSemantics: 'parentesis abierto',
    btnDivide: divide,
    myIcons: MyIcons.parentesisabierto,
  ),
  const ButtonUI(
    btnTxtNoVisible: 'FxHorizontal',
    btnSemantics: 'Funciones',
    btnDivide: divide,
    myIcons: MyIcons.funciones,
  ),
  const ButtonUI(
    btnTxtNoVisible: 'DEL',
    btnSemantics: 'Borrar',
    btnDivide: divide,
    myIcons: MyIcons.borrar,
  ),
  const ButtonUI(
    btnTxtNoVisible: '%',
    btnSemantics: 'porcentaje',
    btnDivide: divide,
    myIcons: MyIcons.porcentajes,
  ),
  const ButtonUI(
    btnTxtNoVisible: ')',
    btnSemantics: 'parentesis cerrado',
    btnDivide: divide,
    myIcons: MyIcons.parentesiscerrado,
  ),
  const ButtonUI(
    btnTxtNoVisible: 'π',
    btnSemantics: 'Pi',
    btnDivide: divide,
    myIcons: MyIcons.conspi,
  ),
];

List<Widget> buttonLandscapeFX_1_2 = [
  const ButtonUI(
    btnTxtNoVisible: 'sin(',
    btnSemantics: 'seno',
    btnDivide: divide,
    myIcons: MyIcons.seno,
  ),
  const ButtonUI(
    btnTxtNoVisible: 'cos(',
    btnSemantics: 'coseno',
    btnDivide: divide,
    myIcons: MyIcons.coseno,
  ),
  const ButtonUI(
    btnTxtNoVisible: 'tan(',
    btnSemantics: 'tangente',
    btnDivide: divide,
    myIcons: MyIcons.tangente,
  ),
  const ButtonUI(
    btnTxtNoVisible: 'FxHorizontal',
    btnSemantics: 'Borrar todo',
    btnDivide: divide,
    myIcons: MyIcons.funciones,
  ),
  const ButtonUI(
    btnTxtNoVisible: 'sec(',
    btnSemantics: 'secante',
    btnDivide: divide,
    myIcons: MyIcons.secante,
  ),
  const ButtonUI(
    btnTxtNoVisible: 'csc(',
    btnSemantics: 'Cosecante',
    btnDivide: divide,
    myIcons: MyIcons.cosecante,
  ),
  const ButtonUI(
    btnTxtNoVisible: 'cot(',
    btnSemantics: 'Cotangente',
    btnDivide: divide,
    myIcons: MyIcons.cotangente,
  ),
  const ButtonUI(
    btnTxtNoVisible: '^',
    btnSemantics: 'x elevado',
    btnDivide: divide,
    myIcons: MyIcons.xn,
  ),
];

List buttonLandscape = [...buttonLandscape_1_2, ...buttonLandscape_3_6];
List buttonLandscapeFX = [...buttonLandscapeFX_1_2, ...buttonLandscape_3_6];

List<Widget> buttonLandscape_3_6 = [
  const ButtonUI(
    btnTxtNoVisible: '7',
    btnSemantics: 'siete',
    btnDivide: divide,
    myIcons: MyIcons.nsiete,
  ),
  const ButtonUI(
    btnTxtNoVisible: '4',
    btnSemantics: 'cuatro',
    btnDivide: divide,
    myIcons: MyIcons.ncuatro,
  ),
  const ButtonUI(
    btnTxtNoVisible: '1',
    btnSemantics: 'uno',
    btnDivide: divide,
    myIcons: MyIcons.nuno,
  ),
  const ButtonUI(
    btnTxtNoVisible: ',',
    btnSemantics: 'coma',
    btnDivide: divide,
    myIcons: MyIcons.coma,
  ),
  const ButtonUI(
    btnTxtNoVisible: '8',
    btnSemantics: 'ocho',
    btnDivide: divide,
    myIcons: MyIcons.nocho,
  ),
  const ButtonUI(
    btnTxtNoVisible: '5',
    btnSemantics: 'cinco',
    btnDivide: divide,
    myIcons: MyIcons.ncinco,
  ),
  const ButtonUI(
    btnTxtNoVisible: '2',
    btnSemantics: 'dos',
    btnDivide: divide,
    myIcons: MyIcons.ndos,
  ),
  const ButtonUI(
    btnTxtNoVisible: '0',
    btnSemantics: 'cero',
    btnDivide: divide,
    myIcons: MyIcons.ncero,
  ),
  const ButtonUI(
    btnTxtNoVisible: '9',
    btnSemantics: 'nueve',
    btnDivide: divide,
    myIcons: MyIcons.nnueve,
  ),
  const ButtonUI(
    btnTxtNoVisible: '6',
    btnSemantics: 'seis',
    btnDivide: divide,
    myIcons: MyIcons.nseis,
  ),
  const ButtonUI(
    btnTxtNoVisible: '3',
    btnSemantics: 'tres',
    btnDivide: divide,
    myIcons: MyIcons.ntres,
  ),
  const ButtonUI(
    btnTxtNoVisible: '=',
    btnSemantics: 'igual',
    btnDivide: divide,
    myIcons: MyIcons.signoigual,
  ),
  const ButtonUI(
    btnTxtNoVisible: '÷',
    btnSemantics: 'Entre',
    btnDivide: divide,
    myIcons: MyIcons.signoentre,
  ),
  const ButtonUI(
    btnTxtNoVisible: 'x',
    btnSemantics: 'Por',
    btnDivide: divide,
    myIcons: MyIcons.signopor,
  ),
  const ButtonUI(
    btnTxtNoVisible: '-',
    btnSemantics: 'Menos',
    btnDivide: divide,
    myIcons: MyIcons.signomenos,
  ),
  const ButtonUI(
    btnTxtNoVisible: '+',
    btnSemantics: 'Mas',
    btnDivide: divide,
    myIcons: MyIcons.signomas,
  ),
];
