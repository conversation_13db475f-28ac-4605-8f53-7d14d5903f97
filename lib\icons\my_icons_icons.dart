/// Flutter icons MyIcons
/// Copyright (C) 2024 by original authors @ fluttericon.com, fontello.com
/// This font was generated by FlutterIcon.com, which is derived from Fontello.
///
/// To use this font, place it in your fonts/ directory and include the
/// following in your pubspec.yaml
///
/// flutter:
///   fonts:
///    - family:  MyIcons
///      fonts:
///       - asset: fonts/MyIcons.ttf
///
/// 
/// * Material Design Icons, Copyright (C) Google, Inc
///         Author:    Google
///         License:   Apache 2.0 (https://www.apache.org/licenses/LICENSE-2.0)
///         Homepage:  https://design.google.com/icons/
///
library;
import 'package:flutter/widgets.dart';

class MyIcons {
  MyIcons._();

  static const _kFontFam = 'MyIcons';
  static const String? _kFontPkg = null;

  
  static const IconData xn = IconData(
    0xe800,
    fontFamily: _kFontFam,
    fontPackage: _kFontPkg,
  );
  static const IconData parentesiscerrado = IconData(0xe801, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData seno = IconData(0xe802, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData coseno = IconData(0xe803, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData secante = IconData(0xe804, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData cotangente = IconData(0xe805, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData cosecante = IconData(0xe806, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData funciones = IconData(0xe807, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData borrartodo = IconData(0xe808, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData tangente = IconData(0xe809, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData xcubico = IconData(0xe80a, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData parentesisdobles = IconData(0xe80b, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData raizcubica = IconData(0xe80c, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData conse = IconData(0xe80d, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData coma = IconData(0xe80e, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData inverso = IconData(0xe80f, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData conspi = IconData(0xe810, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData porcentajes = IconData(0xe811, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData paletacolores = IconData(0xe818, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData borrar = IconData(0xe81a, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData parentesisabierto = IconData(0xe81d, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData ncero = IconData(0xe81e, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData nuno = IconData(0xe81f, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData ndos = IconData(0xe820, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData ntres = IconData(0xe821, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData ncuatro = IconData(0xe822, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData ncinco = IconData(0xe823, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData nseis = IconData(0xe824, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData nsiete = IconData(0xe825, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData nocho = IconData(0xe826, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData nnueve = IconData(0xe827, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData signomas = IconData(0xe828, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData signomenos = IconData(0xe82a, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData signopor = IconData(0xe82b, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData signoentre = IconData(0xe82c, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData signoigual = IconData(0xe82d, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData xcuadrado = IconData(0xe82e, fontFamily: _kFontFam, fontPackage: _kFontPkg);
  static const IconData raizcuadrada = IconData(0xe82f, fontFamily: _kFontFam, fontPackage: _kFontPkg);
}
