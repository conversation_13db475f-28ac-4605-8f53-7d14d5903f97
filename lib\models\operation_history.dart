class OperationHistory {
  final String id;
  final String expression;
  final String result;
  final DateTime date;
  final String? customName; // Nombre personalizado usando T9
  final bool isAutoSaved; // true = guardado automático, false = guardado manual

  OperationHistory({
    required this.id,
    required this.expression,
    required this.result,
    required this.date,
    this.customName,
    this.isAutoSaved = false, // Por defecto es guardado manual
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'expression': expression,
      'result': result,
      'date': date.millisecondsSinceEpoch,
      'customName': customName,
      'isAutoSaved': isAutoSaved ? 1 : 0, // SQLite usa 1/0 para boolean
    };
  }

  factory OperationHistory.fromMap(Map<String, dynamic> map) {
    return OperationHistory(
      id: map['id'],
      expression: map['expression'],
      result: map['result'],
      date: DateTime.fromMillisecondsSinceEpoch(map['date']),
      customName: map['customName'],
      isAutoSaved: (map['isAutoSaved'] ?? 0) == 1, // Convertir de 1/0 a boolean
    );
  }

  OperationHistory copyWith({
    String? id,
    String? expression,
    String? result,
    DateTime? date,
    String? customName,
    bool? isAutoSaved,
  }) {
    return OperationHistory(
      id: id ?? this.id,
      expression: expression ?? this.expression,
      result: result ?? this.result,
      date: date ?? this.date,
      customName: customName ?? this.customName,
      isAutoSaved: isAutoSaved ?? this.isAutoSaved,
    );
  }
}
