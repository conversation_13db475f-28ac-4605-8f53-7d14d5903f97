import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../provider/calculator_provider.dart';
import 'operaciones_linea1_linea2.dart';

class PortraitHome extends StatelessWidget {
  const PortraitHome({super.key});

  @override
  Widget build(BuildContext context) {
    CalculatorProvider watch = context.watch<CalculatorProvider>();

    return Semantics(
      label: 'Pantalla y Teclado',
      child: Column(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            child: SizedBox(
              child: Container(
                margin: const EdgeInsets.symmetric(horizontal: 4),
                width: double.maxFinite,
                decoration: BoxDecoration(
                    border: Border.all(),
                    borderRadius: BorderRadius.circular(20.0)),
                child: OperacionesLinea1Linea2(watch: watch),
              ),
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: Wrap(
              runSpacing: 8.0,
              spacing: 8.0,
              children: List.generate(
                  watch.newListButtonProviderVertical.length,
                  (index) => watch.newListButtonProviderVertical[index]),
            ),
          ),
        ],
      ),
    );
  }
}
