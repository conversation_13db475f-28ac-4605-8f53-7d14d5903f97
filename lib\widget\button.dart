import 'package:calculadora_xxi/data/list_button_landscape.dart';
import 'package:calculadora_xxi/provider/calculator_provider.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../data/list_button_portrait.dart';

class ButtonUI extends StatelessWidget {
  const ButtonUI(
      {super.key,
      required this.btnTxtNoVisible,
      required this.btnSemantics,
      required this.btnDivide,
      required this.myIcons});

  final String btnTxtNoVisible;
  final String btnSemantics;
  final int btnDivide;
  final IconData myIcons;

  @override
  Widget build(BuildContext context) {
    final myScrrenMin = MediaQuery.of(context).size.shortestSide;

    if (btnTxtNoVisible == 'FxVertical') {
      return InkWell(
        onTap: () =>
            context.read<CalculatorProvider>().setNewListsV(buttonPortraitFX),
        child: BotonDesing(
            myScrrenMin: myScrrenMin,
            btnDivide: btnDivide,
            btnSemantics: btnSemantics,
            myIcons: myIcons),
      );
    }
    if (btnTxtNoVisible == 'FxHorizontal') {
      return InkWell(
        onTap: () =>
            context.read<CalculatorProvider>().setNewListsH(buttonLandscapeFX),
        child: BotonDesing(
            myScrrenMin: myScrrenMin,
            btnDivide: btnDivide,
            btnSemantics: btnSemantics,
            myIcons: myIcons),
      );
    } else {
      return InkWell(
        onTap: () =>
            context.read<CalculatorProvider>().setValues(btnTxtNoVisible),
        child: BotonDesing(
            myScrrenMin: myScrrenMin,
            btnDivide: btnDivide,
            btnSemantics: btnSemantics,
            myIcons: myIcons),
      );
    }
  }
}

class BotonDesing extends StatelessWidget {
  const BotonDesing({
    super.key,
    required this.myScrrenMin,
    required this.btnDivide,
    required this.btnSemantics,
    required this.myIcons,
  });

  final double myScrrenMin;
  final int btnDivide;
  final String btnSemantics;
  final IconData myIcons;

  @override
  Widget build(BuildContext context) {
    return Container(
      // alignment: Alignment.center,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
      ),
      width: myScrrenMin * 0.8 / btnDivide,
      height: myScrrenMin * .8 / 5,
      child: Semantics(
        button: true,
        label: btnSemantics,
        child: ExcludeSemantics(
            child: Icon(
          myIcons,
          size: 32,
        )),
      ),
    );
  }
}
