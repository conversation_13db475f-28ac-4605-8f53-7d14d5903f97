import 'package:flutter/material.dart';
import 'package:function_tree/function_tree.dart';
import '../data/list_button_landscape.dart';
import '../data/list_button_portrait.dart';
import 'history_provider.dart';

class CalculatorProvider extends ChangeNotifier {
  final operacionesProvider = TextEditingController();

  final resultadosProvider = TextEditingController();

  HistoryProvider? historyProvider;

  // Constructor para inicializar el cursor
  CalculatorProvider() {
    // Establecer cursor inicial visible al inicio de la app
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _setInitialCursor();
    });
  }

  // Para rastrear si estamos editando una operación existente
  String? _editingOperationId;
  String? get editingOperationId => _editingOperationId;
  //String get newValueBoton => '';

  List listButtonProviderVertical = buttonPortrait;
  List listButtonProviderHorizontal = buttonLandscape;

  List get newListButtonProviderVertical => listButtonProviderVertical;
  List get newListButtonProviderHorizontal => listButtonProviderHorizontal;

  //setValue(String value) {
  //String str = operacionesProvider.text;
  Future<void> setNewListsV(List newList) async {
    if (listButtonProviderVertical == newList) {
      newList = buttonPortrait;
    }

    listButtonProviderVertical = newList;

    notifyListeners();
  }

  Future<void> setNewListsH(List newList) async {
    if (listButtonProviderHorizontal == newList) {
      newList = buttonLandscape;
    }

    listButtonProviderHorizontal = newList;
    notifyListeners();
  }

  String get str => operacionesProvider.text;

  Future<void> setValues(String newValueBoton) async {
    switch (newValueBoton) {
      case 'C':
        operacionesProvider.clear();
        resultadosProvider.clear();
        break;

      case 'DEL':
        // Usar la nueva función de eliminación con cursor
        deleteAtCursor();
        break;

      case '÷':
        revSignos('÷');
        break;

      case 'x':
        revSignos('x');
        break;

      case '-':
        revSignos('-');
        break;

      case '+':
        revSignos('+');
        break;

      case '%':
        _handlePercentage();
        break;

      case '^':
        if (str.isEmpty) break;
        final String lastWord = str.substring((str.length - 1));
        if (lastWord == '^') break;

        
        // Inserta el símbolo '^' sin llamar a calcular() para evitar errores.
        final currentText = operacionesProvider.text;
        final selection = operacionesProvider.selection;
        final newText = currentText.replaceRange(
          selection.start,
          selection.end,
          newValueBoton,
        );
        operacionesProvider.value = TextEditingValue(
          text: newText,
          selection: TextSelection.collapsed(
            offset: selection.start + newValueBoton.length,
          ),
        );

        // Reinicia los botones a la vista principal, igual que las funciones trigonométricas.
        setNewListsV(buttonPortrait);
        setNewListsH(buttonLandscape);
            
        break;

      case '(':
        insertAtCursor(newValueBoton);
        break;
      case ')':
        if (str.isEmpty) break;

        final openParentesis = str.runes
            .where((rune) => rune == '('.runes.first)
            .length;
        final closeParentesis = str.runes
            .where((rune) => rune == ')'.runes.first)
            .length;
        if (openParentesis == closeParentesis) {
          operacionesProvider.text = '($str$newValueBoton';
        } else {
          insertAtCursor(newValueBoton);
        }

        calcular();
        break;


      case '√(':
        insertAtCursor(newValueBoton);
        break;

      case '⅓':
        operacionesProvider.text = '($str)^⅓';
        setNewListsV(buttonPortrait);
        calcular();
        break;

      case 'π':
        if (str.isEmpty) break;

        final String lastWord = str.substring((str.length - 1), (str.length));

        if (str.isNotEmpty && lastWord == 'π') {
          operacionesProvider.text = str.substring(0, str.length - 1);
        }
        insertAtCursor(newValueBoton);

        calcular();

        break;

      case ',':
        if (str.isNotEmpty) {
          final a = str.substring(str.lastIndexOf(',') + 1).toString();
          if (a.contains('+') ||
              a.contains('-') ||
              a.contains('x') ||
              a.contains('÷') ||
              (!str.contains(','))) {
            insertAtCursor(',');
          }
        }
        break;

      case '=':
        if (str.isEmpty) break;
        if (str.isEmpty) return;
        final String lastWord = str.substring((str.length - 1), (str.length));

        if (str.isNotEmpty && lastWord == '+' ||
            lastWord == '-' ||
            lastWord == 'x' ||
            lastWord == '÷') {
          operacionesProvider.text = str.substring(0, str.length - 1);

          return;
        }

        // Guardar la expresión original antes de calcular
        final String originalExpression = str;

        calcular();

        // Guardar automáticamente la operación en el historial
        if (resultadosProvider.text.isNotEmpty &&
            originalExpression.isNotEmpty) {
          _saveAutoOperation(originalExpression, resultadosProvider.text);
        }

        operacionesProvider.text = resultadosProvider.text;
        break;

      case 'sin(':
        if (str.isEmpty) return;
        calculosTrigonometricos(newValueBoton);
        break;

      case 'sec(':
        if (str.isEmpty) return;
        calculosTrigonometricos(newValueBoton);
        break;

      case 'cos(':
        if (str.isEmpty) return;
        calculosTrigonometricos(newValueBoton);
        break;
      case 'csc(':
        if (str.isEmpty) return;
        calculosTrigonometricos(newValueBoton);
        break;

      case 'tan(':
        if (str.isEmpty) return;
        calculosTrigonometricos(newValueBoton);
        break;

      case 'cot(':
        if (str.isEmpty) return;
        calculosTrigonometricos(newValueBoton);
        break;

      default:
        insertAtCursor(newValueBoton);
    }
  }

  void calculosTrigonometricos(String newValueBoton) {
    operacionesProvider.text = '$newValueBoton($str)*π/180)';
    setNewListsV(buttonPortrait);
    setNewListsH(buttonLandscape);
    calcular();
  }

  calcular() {
    final openParentesis = str.runes
        .where((rune) => rune == '('.runes.first)
        .length;
    final closeParentesis = str.runes
        .where((rune) => rune == ')'.runes.first)
        .length;
    if (openParentesis != closeParentesis) {
      resultadosProvider.text = str;
      return;
    }

    String cal;
    cal = str;
    cal = cal.replaceAll('x', '*');
    cal = cal.replaceAll('÷', '/');
    cal = cal.replaceAll(',', '.');

    cal = cal.replaceAll(')√(', ')*√(');
    cal = cal.replaceAll(')(', ')*(');
    // int * (
    cal = cal.replaceAll('1(', '1*(');
    cal = cal.replaceAll('2(', '2*(');
    cal = cal.replaceAll('3(', '3*(');
    cal = cal.replaceAll('4(', '4*(');
    cal = cal.replaceAll('5(', '5*(');
    cal = cal.replaceAll('6(', '6*(');
    cal = cal.replaceAll('7(', '7*(');
    cal = cal.replaceAll('8(', '8*(');
    cal = cal.replaceAll('9(', '9*(');
    cal = cal.replaceAll('0(', '0*(');
    cal = cal.replaceAll('π(', 'pi*(');

    // int * pi

    cal = cal.replaceAll('1π', '1*pi');
    cal = cal.replaceAll('2π', '2*pi');
    cal = cal.replaceAll('3π', '3*pi');
    cal = cal.replaceAll('4π', '4*pi');
    cal = cal.replaceAll('5π', '5*pi');
    cal = cal.replaceAll('6π', '6*pi');
    cal = cal.replaceAll('7π', '7*pi');
    cal = cal.replaceAll('8π', '8*pi');
    cal = cal.replaceAll('9π', '9*pi');
    cal = cal.replaceAll('0π', '0');
    

    // int * pi

    cal = cal.replaceAll('π', 'pi');

    cal = cal.replaceAll('1√(', '1*sqrt(');
    cal = cal.replaceAll('2√(', '2*sqrt(');
    cal = cal.replaceAll('3√(', '3*sqrt(');
    cal = cal.replaceAll('4√(', '4*sqrt(');
    cal = cal.replaceAll('5√(', '5*sqrt(');
    cal = cal.replaceAll('6√(', '6*sqrt(');
    cal = cal.replaceAll('7√(', '7*sqrt(');
    cal = cal.replaceAll('8√(', '8*sqrt(');
    cal = cal.replaceAll('9√(', '9*sqrt(');
    cal = cal.replaceAll('0√(', '0*sqrt(');
    cal = cal.replaceAll('√(', 'sqrt(');
    cal = cal.replaceAll('^⅓', '^(1/3)');

    resultadosProvider.text = cal.interpret().toStringAsFixed(10);
    resultadosProvider.text = double.parse(resultadosProvider.text).toString();
    resultadosProvider.text = resultadosProvider.text.replaceAll('.', ',');
  }

  revSignos(String operator) {
    if (str.isEmpty) return;

    final String lastWord = str.substring((str.length - 1), (str.length));

    if (str.isNotEmpty && lastWord == '+' ||
        lastWord == '-' ||
        lastWord == 'x' ||
        lastWord == '÷') {

      // Eliminar el operador anterior y agregar el nuevo
      operacionesProvider.text = str.substring(0, str.length - 2);
      insertAtCursor(' $operator');
      return;
    }

    insertAtCursor(' $operator');
  }

//Calculo del porcentaje
_handlePercentage() {
    if (str.isEmpty) return;

    // Regex para encontrar el patrón: "número operador número" al final del string.
    // Funciona con o sin espacios. Ej: "500-25" o "(500 - 25"
    final regex = RegExp(r'([\d.,]+)\s*([-+x÷])\s*([\d.,]+)$');
    final match = regex.firstMatch(str);

    if (match != null) {
      // CASO 1: Hay una operación como "500 - 25" y se presiona %
      final fullMatchText = match.group(
        0,
      )!; // El texto que coincidió (ej: " - 25")
      final baseNumberStr = match.group(1)!.replaceAll(',', '.');
      final operator = match.group(2)!;
      final percentageNumberStr = match.group(3)!.replaceAll(',', '.');

      final baseNumber = double.tryParse(baseNumberStr);
      final percentageNumber = double.tryParse(percentageNumberStr);

      if (baseNumber != null && percentageNumber != null) {
        double calculatedValue;
        if (operator == '+' || operator == '-') {
          // Para suma/resta: 50% de 500 es 250
          calculatedValue = baseNumber * (percentageNumber / 100);
        } else {
          // Para mult/div: 100 * 50% es 100 * 0.5
          calculatedValue = percentageNumber / 100;
        }

        // Reemplaza la última parte de la expresión por el valor ya calculado
        final prefix = str.substring(0, str.length - fullMatchText.length);
        final newExpression =
            '$prefix$baseNumberStr $operator ${calculatedValue.toString().replaceAll('.', ',')}';

        // Coloca el texto y mueve el cursor al final
        operacionesProvider.value = TextEditingValue(
          text: newExpression,
          selection: TextSelection.fromPosition(
            TextPosition(offset: newExpression.length),
          ),
        );
        
        calcular(); // Actualiza el resultado final
      }
    } else {
      // CASO 2: Solo hay un número, ej: "25" -> se convierte en 0.25
      final number = double.tryParse(str.replaceAll(',', '.'));
      if (number != null) {

        final newText = (number / 100).toString().replaceAll('.', ',');

        // Coloca el texto y mueve el cursor al final
        operacionesProvider.value = TextEditingValue(
          text: newText,
          selection: TextSelection.fromPosition(
            TextPosition(offset: newText.length),
          ),
        );
        calcular();
      }
    }
  }




  // Método para cargar una operación para editar
  void loadOperationForEditing(String operationId, String expression) {
    _editingOperationId = operationId;
    operacionesProvider.text = expression;
    resultadosProvider.clear();
    notifyListeners();
  }

  // Método para limpiar el estado de edición
  void clearEditingState() {
    _editingOperationId = null;
    notifyListeners();
  }

  // Método para verificar si hay una operación siendo editada
  bool get isEditingOperation => _editingOperationId != null;

  // Método para guardar automáticamente operaciones cuando se presiona =
  void _saveAutoOperation(String expression, String result) {
    if (historyProvider != null) {
      historyProvider!.saveAutoOperation(expression, result);
    }
  }

  // Método para establecer cursor inicial visible (solo al inicio de la app)
  void _setInitialCursor() {
    // Solo establecer cursor si el campo está vacío (inicio de la app)
    if (operacionesProvider.text.isEmpty) {
      operacionesProvider.selection = TextSelection.fromPosition(
        const TextPosition(offset: 0),
      );
    }
  }

  // Métodos para edición avanzada con cursor
  void insertAtCursor(String text) {
    final currentText = operacionesProvider.text;
    final selection = operacionesProvider.selection;

    if (selection.isValid) {
      final newText = currentText.replaceRange(
        selection.start,
        selection.end,
        text,
      );

      operacionesProvider.value = TextEditingValue(
        text: newText,
        selection: TextSelection.collapsed(
          offset: selection.start + text.length,
        ),
      );
    } else {
      // Si no hay selección válida, agregar al final
      operacionesProvider.text += text;
    }

    // Recalcular después de insertar
    calcular();
    notifyListeners();
  }

  void deleteAtCursor() {
    final currentText = operacionesProvider.text;
    final selection = operacionesProvider.selection;

    if (selection.isValid && selection.start > 0) {
      String newText;
      int newCursorPosition;

      if (selection.isCollapsed) {
        // Eliminar un carácter antes del cursor
        newText = currentText.replaceRange(
          selection.start - 1,
          selection.start,
          '',
        );
        newCursorPosition = selection.start - 1;
      } else {
        // Eliminar texto seleccionado
        newText = currentText.replaceRange(selection.start, selection.end, '');
        newCursorPosition = selection.start;
      }

      operacionesProvider.value = TextEditingValue(
        text: newText,
        selection: TextSelection.collapsed(offset: newCursorPosition),
      );

      // Recalcular después de eliminar
      if (newText.isNotEmpty) {
        calcular();
      } else {
        resultadosProvider.clear();
      }
    }

    notifyListeners();
  }

  @override
  void dispose() {
    super.dispose();
    operacionesProvider.dispose();
    resultadosProvider.dispose();
  }
}
