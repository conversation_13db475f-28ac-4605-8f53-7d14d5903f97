import 'package:flutter/material.dart';
import '../models/operation_history.dart';
import '../services/database_service.dart';

class HistoryProvider extends ChangeNotifier {
  List<OperationHistory> _operations = [];
  DateTime _selectedDate = DateTime.now();
  List<DateTime> _datesWithOperations = [];
  List<DateTime> _datesWithNamedOperations = [];

  List<OperationHistory> get operations => _operations;
  DateTime get selectedDate => _selectedDate;
  List<DateTime> get datesWithOperations => _datesWithOperations;
  List<DateTime> get datesWithNamedOperations => _datesWithNamedOperations;

  Future<void> loadOperationsByDate(DateTime date) async {
    _selectedDate = date;
    _operations = await DatabaseService.getOperationsByDate(date);
    notifyListeners();
  }

  Future<void> loadAllOperations() async {
    _operations = await DatabaseService.getAllOperations();
    notifyListeners();
  }

  Future<void> loadDatesWithOperations() async {
    _datesWithOperations = await DatabaseService.getDatesWithOperations();
    _datesWithNamedOperations =
        await DatabaseService.getDatesWithNamedOperations();
    notifyListeners();
  }

  // Guardar operación manual (con botón Guardar)
  Future<void> saveOperation(String expression, String result) async {
    final operation = OperationHistory(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      expression: expression,
      result: result,
      date: DateTime.now(),
      isAutoSaved: false, // Guardado manual
    );

    await DatabaseService.insertOperation(operation);

    // Recargar operaciones del día actual
    await loadOperationsByDate(_selectedDate);
    await loadDatesWithOperations();
  }

  // Guardar operación automática (cuando se presiona =)
  Future<void> saveAutoOperation(String expression, String result) async {
    await DatabaseService.insertAutoOperation(expression, result);

    // Recargar operaciones del día actual
    await loadOperationsByDate(_selectedDate);
    await loadDatesWithOperations();
  }

  // Convertir operación automática en manual (cuando se edita y guarda con nombre)
  Future<void> convertToManualOperation(
    String expression,
    String result,
    String customName,
  ) async {
    final operation = OperationHistory(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      expression: expression,
      result: result,
      date: DateTime.now(),
      customName: customName,
      isAutoSaved: false, // Ahora es manual
    );

    await DatabaseService.insertOperation(operation);

    // Recargar operaciones del día actual
    await loadOperationsByDate(_selectedDate);
    await loadDatesWithOperations();
  }

  Future<void> updateOperationName(String id, String customName) async {
    await DatabaseService.updateOperationName(id, customName);

    // Actualizar la operación en la lista local
    final index = _operations.indexWhere((op) => op.id == id);
    if (index != -1) {
      _operations[index] = _operations[index].copyWith(customName: customName);
      notifyListeners();
    }
  }

  Future<void> updateOperation(
    String id,
    String expression,
    String result,
    String? customName,
  ) async {
    await DatabaseService.updateOperation(id, expression, result, customName);

    // Actualizar la operación en la lista local
    final index = _operations.indexWhere((op) => op.id == id);
    if (index != -1) {
      _operations[index] = _operations[index].copyWith(
        expression: expression,
        result: result,
        customName: customName,
      );
      notifyListeners();
    }
  }

  Future<void> deleteOperation(String id) async {
    await DatabaseService.deleteOperation(id);
    _operations.removeWhere((op) => op.id == id);
    await loadDatesWithOperations();
    notifyListeners();
  }

  void setSelectedDate(DateTime date) {
    _selectedDate = date;
    loadOperationsByDate(date);
  }
}
